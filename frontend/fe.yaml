apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: apartment-fe
  name: apartment-fe
spec:
  replicas: 2
  selector:
    matchLabels:
      app: apartment-fe
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: apartment-fe
    spec:
      imagePullSecrets:
      - name: regcred
      containers:
      - image: ghcr.io/diskyayyay/apartment-fe:0.1.7
        name: apartment-fe
        resources: {}
        ports:
        - containerPort: 3000
status: {}
