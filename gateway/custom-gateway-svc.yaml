apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: nginx-gateway
    app.kubernetes.io/managed-by: nginx-gateway-nginx
    app.kubernetes.io/name: apartment-gateway-nginx
    gateway.networking.k8s.io/gateway-name: apartment-gateway
  name: custom-apartment-gateway-nginx
  namespace: nginx-gateway
spec:
  ports:
  - name: port-80
    nodePort: 31114
    port: 80
    protocol: TCP
    targetPort: 80
  - name: port-443
    nodePort: 31115
    port: 443
    protocol: TCP
    targetPort: 443
  selector:
    app.kubernetes.io/instance: nginx-gateway
    app.kubernetes.io/managed-by: nginx-gateway-nginx
    app.kubernetes.io/name: apartment-gateway-nginx
    gateway.networking.k8s.io/gateway-name: apartment-gateway
  type: NodePort
