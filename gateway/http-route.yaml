apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: apartment-http-filter-redirect
spec:
  parentRefs:
  - name: apartment-gateway
    namespace: nginx-gateway
    sectionName: http
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: "/"
    backendRefs:
    - name: apartment-fe
      port: 3000
  - matches:
    - path:
        type: PathPrefix
        value: "/api"
    backendRefs:
    - name: apartment-be
      port: 3001
