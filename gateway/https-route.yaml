apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: apartment-https-route
spec:
  parentRefs:
  - name: apartment-gateway
    sectionName: https
    namespace: nginx-gateway
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: "/"
    backendRefs:
    - name: apartment-fe
      port: 3000
  - matches:
    - path:
        type: PathPrefix
        value: "/api"
    filters:
    - type: URLRewrite
      urlRewrite:
        path:
          type: ReplacePrefixMatch
          replacePrefixMatch: /
    backendRefs:
    - name: apartment-be
      port: 3001
