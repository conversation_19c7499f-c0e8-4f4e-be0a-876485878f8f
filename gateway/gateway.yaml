apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: apartment-gateway
  namespace: nginx-gateway
spec:
  gatewayClassName: nginx
  listeners:
  - name: http
    protocol: HTTP
    port: 80
    allowedRoutes:
      namespaces:
        from: All
  - name: https
    protocol: HTTPS
    port: 443
    tls:
      certificateRefs:
      - kind: Secret
        name: tls-apartment-secret
    allowedRoutes:
      namespaces:
        from: All
