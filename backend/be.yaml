apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    app: apartment-be
  name: apartment-be
spec:
  replicas: 2
  selector:
    matchLabels:
      app: apartment-be
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: apartment-be
    spec:
      imagePullSecrets:
      - name: regcred
      containers:
      - image: ghcr.io/diskyayyay/apartment-be:0.1.1
        name: apartment-be
        env:
        - name: PORT
          value: "3001"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: apartment-be-secret
              key: DATABASE_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: apartment-be-secret
              key: JWT_SECRET
        - name: JWT_EXPIRES_IN
          valueFrom:
            secretKeyRef:
              name: apartment-be-secret
              key: JWT_EXPIRES_IN
        ports:
        - containerPort: 3001
        resources: {}
status: {}
