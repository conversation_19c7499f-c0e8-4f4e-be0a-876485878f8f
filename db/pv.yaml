apiVersion: v1
kind: PersistentVolume
metadata:
  name: db-pv
spec:
  storageClassName: manual
  capacity:
    storage: 5Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  hostPath:
    path: "/mnt/data"
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: nodename
          operator: In
          values:
          - worker1
