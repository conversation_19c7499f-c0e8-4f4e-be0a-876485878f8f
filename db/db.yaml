apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: apartment-db
spec:
  volumeClaimTemplates:
    - metadata:
        name: pgdata
      spec:
        storageClassName: manual
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 5Gi
  serviceName: "postgres"
  replicas: 1
  selector:
    matchLabels:
      app: apartment-db
  template:
    metadata:
      labels:
        app: apartment-db
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: nodename
                operator: In
                values:
                - worker1
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: pg-config
              key: POSTGRES_DB
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: pg-secret
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: pgdata
          mountPath: /data

